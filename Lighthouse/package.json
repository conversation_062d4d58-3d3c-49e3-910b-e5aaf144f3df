{"name": "tanstack-start-example-basic", "private": true, "sideEffects": false, "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "start": "vite start"}, "dependencies": {"@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@tailwindcss/postcss": "^4.1.4", "@tanstack/react-query": "^5.81.5", "@tanstack/react-query-devtools": "^5.81.5", "@tanstack/react-router": "^1.116.0", "@tanstack/react-router-devtools": "^1.116.0", "@tanstack/react-start": "^1.116.1", "@tanstack/start": "^1.116.1", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "immer": "^10.1.1", "lucide-react": "^0.488.0", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-force-graph-2d": "^1.28.0", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "zustand": "^5.0.6"}, "devDependencies": {"@types/node": "^22.5.4", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "autoprefixer": "^10.4.20", "postcss": "^8.5.3", "tailwindcss": "^4.1.4", "typescript": "^5.7.2", "vite-tsconfig-paths": "^5.1.4"}}
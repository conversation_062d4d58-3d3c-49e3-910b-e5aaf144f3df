{"name": "@luminar/amna-dashboard", "version": "1.0.0", "description": "AMNA (AI Multi-Agent Nexus Architecture) Dashboard Module", "main": "index.ts", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ui": "jest --coverage --watchAll=false && open coverage/lcov-report/index.html", "lint": "eslint . --ext .ts,.tsx", "type-check": "tsc --noEmit"}, "dependencies": {"@monaco-editor/react": "^4.6.0", "@tanstack/react-query": "^5.17.0", "axios": "^1.6.0", "clsx": "^2.1.0", "date-fns": "^3.0.0", "lucide-react": "^0.300.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-flow-renderer": "^10.3.17", "react-hot-toast": "^2.4.1", "recharts": "^2.10.0", "socket.io-client": "^4.7.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.0", "@testing-library/react": "^14.1.0", "@testing-library/user-event": "^14.5.0", "@types/jest": "^29.5.0", "@types/node": "^20.10.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "ts-jest": "^29.1.0", "typescript": "^5.3.0"}, "peerDependencies": {"tailwindcss": "^3.4.0"}, "repository": {"type": "git", "url": "git+https://github.com/luminar/command-center.git"}, "keywords": ["amna", "ai", "multi-agent", "dashboard", "react", "typescript"], "author": "Luminar Team", "license": "MIT"}
{"name": "@luminar/training-needs", "version": "1.0.0", "private": true, "description": "Training Need Analysis application for Luminar L&D", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit"}, "dependencies": {"@luminar/shared-auth": "file:../shared-auth", "@luminar/shared-ui": "file:../shared-ui", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.0", "axios": "^1.6.0", "recharts": "^2.10.0", "react-hook-form": "^7.48.0", "zod": "^3.22.0", "@hookform/resolvers": "^3.3.0", "date-fns": "^2.30.0", "clsx": "^2.0.0", "tailwind-merge": "^2.1.0"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "tailwindcss": "^3.3.0", "typescript": "^5.2.2", "vite": "^5.0.8", "vitest": "^1.0.0"}}
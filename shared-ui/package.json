{"name": "luminar-ui", "private": true, "sideEffects": false, "type": "module", "bin": {"luminar": "./cli/index.js"}, "scripts": {"dev": "vite dev --config vite.config.dev.ts", "dev:watch": "vite dev --config vite.config.dev.ts --watch", "build": "vite build", "build:analyze": "vite build && open dist/stats.html", "start": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "test:run": "vitest run", "lint": "eslint . --ext ts,tsx,js,jsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx,js,jsx --fix", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,css,md}\"", "typecheck": "tsc --noEmit", "prepare": "husky install", "quality:check": "node scripts/quality-check.js", "quality:check:simple": "npm run lint && npm run typecheck && npm run test:run && npm run build", "storybook": "NODE_ENV=development STORYBOOK=true storybook dev -p 6006", "build-storybook": "storybook build", "playground": "vite dev --open /playground --config vite.config.dev.ts", "showcase": "vite dev --open /showcase --config vite.config.dev.ts", "performance": "vite dev --open /performance --config vite.config.dev.ts", "docs": "vite dev --open /documentation --config vite.config.dev.ts"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-slot": "^1.2.0", "@react-three/fiber": "^9.2.0", "@tailwindcss/postcss": "^4.1.4", "@tanstack/react-router": "^1.116.0", "@tanstack/react-router-devtools": "^1.116.0", "@tanstack/react-start": "^1.116.1", "@tanstack/router-generator": "^1.124.0", "@tanstack/start": "^1.116.1", "@types/three": "^0.178.0", "ai": "^4.3.16", "chalk": "^5.3.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "commander": "^12.0.0", "framer-motion": "^12.23.0", "fs-extra": "^11.2.0", "glob": "^10.3.10", "lucide-react": "^0.488.0", "ogl": "^0.0.79", "ora": "^8.0.1", "prettier": "^3.2.5", "prompts": "^2.4.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "semver": "^7.5.4", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "three": "^0.178.0", "vite": "^6.0.0"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/js": "^9.30.1", "@storybook/addon-a11y": "^9.0.16", "@storybook/react-vite": "^9.0.15", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/lodash": "^4.17.20", "@types/node": "^22.5.4", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@vitejs/plugin-react": "^4.6.0", "@webgpu/types": "^0.1.64", "autoprefixer": "^10.4.20", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "husky": "^9.1.7", "jsdom": "^26.1.0", "lint-staged": "^16.1.2", "lodash": "^4.17.21", "postcss": "^8.5.3", "rollup-plugin-visualizer": "^6.0.3", "storybook": "^9.0.15", "tailwindcss": "^4.1.4", "typescript": "^5.7.2", "vite-plugin-pwa": "^1.0.1", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4", "workbox-window": "^7.3.0", "zod": "^3.25.71"}}